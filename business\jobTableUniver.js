/**
 * 基础工具类 - 提供通用的验证和获取方法
 */
class BaseUtils {
  /**
   * 验证 univerAPI 是否已初始化
   * @param {Object} univerAPI - univerAPI 实例
   * @returns {boolean} 是否已初始化
   */
  static validateUniverAPI(univerAPI) {
    if (!univerAPI) {
      return false;
    }
    return true;
  }

  /**
   * 验证列号参数
   * @param {number} columnNumber - 列号
   * @returns {boolean} 是否有效
   */
  static validateColumnNumber(columnNumber) {
    if (typeof columnNumber !== "number" || columnNumber < 0) {
      return false;
    }
    return true;
  }

  /**
   * 验证行号参数
   * @param {number} rowNumber - 行号
   * @returns {boolean} 是否有效
   */
  static validateRowNumber(rowNumber) {
    if (typeof rowNumber !== "number" || rowNumber < 0) {
      return false;
    }
    return true;
  }

  /**
   * 计算表头行数
   * @param {Object} sourceData - 源数据
   * @returns {number} 表头行数
   */
  static getHeaderRowCount(sourceData) {
    return sourceData && sourceData.headers ? sourceData.headers.length : 0;
  }
}

/**
 * 数据转换器 - 负责数据格式转换
 */
class DataConverter {
  /**
   * Univer 数据格式转换器
   * 将标准化数据转换为 Univer 所需的 cellData 格式
   */
  formatCellData(headers, cells, headerDictionaryIndex) {
    const cellData = {};

    // 处理表头数据
    this._formatHeaderCells(cellData, cells, headers, headerDictionaryIndex);

    // 处理数据行
    this._formatDataCells(cellData, cells, headers, headerDictionaryIndex);

    return cellData;
  }

  /**
   * 格式化表头单元格
   * @param {Object} cellData - 单元格数据对象
   * @param {Array} headers - 表头数据
   * @param {number} headerDictionaryIndex - 表头字典索引
   * @private
   */
  _formatHeaderCells(cellData, cells, headers, headerDictionaryIndex) {
    headers.forEach((headerRow, rowIndex) => {
      cellData[rowIndex] = {};

      headerRow.forEach((category, colIndex) => {
        cellData[rowIndex][colIndex] = {
          v: category.name,
          s: this._createCellStyle("header", {
            columnConfig: category,
          }),
          custom: {
            sql_column: category.sql_column,
            ...category,
          },
        };
      });
    });
  }

  /**
   * 格式化数据单元格
   * @param {Object} cellData - 单元格数据对象
   * @param {Array} cells - 数据行
   * @param {Array} headers - 表头数据
   * @param {number} headerDictionaryIndex - 表头字典索引
   * @private
   */
  _formatDataCells(cellData, cells, headers, headerDictionaryIndex) {
    const headerConfig = headers[headerDictionaryIndex];

    cells.forEach((job, index) => {
      const rowIndex = headers.length + index;
      cellData[rowIndex] = {};

      headerConfig.forEach((columnConfig, colIndex) => {
        const cellValue = job[columnConfig.sql_column] || "";

        cellData[rowIndex][colIndex] = {
          v: cellValue || undefined,
          s: this._createCellStyle("data", { columnConfig, colIndex }),
          custom: {
            id: job.id,
            job_id: job.job_id,
          },
          ...(columnConfig.type === "number" && { t: 2 }), // 数字类型
        };
      });
    });
  }

  /**
   * 统一创建单元格样式
   * @param {string} cellType - 单元格类型：'header' 或 'data'
   * @param {Object} options - 样式选项
   * @param {boolean} options.isGrayText - 是否为灰色文本（表头用）
   * @param {Object} options.columnConfig - 列配置（数据行用）
   * @param {number} options.colIndex - 列索引（数据行用）
   * @returns {Object} 样式对象
   * @private
   */
  _createCellStyle(cellType, options = {}) {
    const { columnConfig, colIndex } = options;

    if (cellType === "header") {
      const headerConfig = {
        is_gray: 1,
        ...columnConfig,
      };

      return this._createHeaderStyle(headerConfig, colIndex);
    } else if (cellType === "data") {
      const dataConfig = {
        columnConfig: columnConfig,
        colIndex: colIndex,
      };

      return this._createDataStyle(dataConfig);
    }

    throw new Error(`未知的单元格类型: ${cellType}`);
  }

  /**
   * 创建表头样式
   * @param {Object} headerConfig - 表头配置对象
   * @param {number} headerConfig.is_gray - 是否为灰色文本 (1=灰色, 0=正常)
   * @returns {Object} 样式对象
   * @private
   */
  _createHeaderStyle(headerConfig, colIndex) {
    const style = {
      ff: "Arial", // 字体名称为 Arial
      ht: 2, // 水平居中
      vt: 2, // 垂直居中
      tb: 2, // 截断
      bd: {
        // 上边框
        t: {
          s: 1, // 边框样式
          cl: {
            // 边框颜色
            rgb: "#DEE0E3",
          },
        },
        // 左边框
        r: {
          s: 1, // 边框样式
          cl: {
            // 边框颜色
            rgb: "#DEE0E3",
          },
        },
        // 左边框
        l: {
          s: 1, // 边框样式
          cl: {
            // 边框颜色
            rgb: "#DEE0E3",
          },
        },
      },
      pd: {
        t: 10, // 上边距
        b: 10, // 下边距
        l: 28,
      },
      cl: { rgb: "#222222" },
      bg: {
        rgb: headerConfig.bg_color ? "#DDE2F9" : "#F2F4F5",
      },
    };

    // 灰色文本时添加右边距
    // if (headerConfig.is_gray === 1) {
    //   style.pd.r = 18;
    // }

    return style;
  }

  /**
   * 创建数据行样式
   * @param {Object} dataConfig - 数据行配置对象
   * @param {Object} dataConfig.columnConfig - 列配置
   * @param {number} dataConfig.colIndex - 列索引
   * @returns {Object} 样式对象
   * @private
   */
  _createDataStyle(dataConfig) {
    const { columnConfig, colIndex } = dataConfig;

    const style = {
      ff: "Arial", // 字体名称为 Arial
      tb: 2, // 截断
      fs: 10, // 字体大小

      //
      pd: {
        t: 6, // 上边距
        b: 6, // 下边距
        l: 10,
        r: 10,
      },
      vt: 2, // 垂直对齐：1=顶部, 2=居中, 3=底部
      cl: this._getTextColor(columnConfig),
      bd: {
        // 上边框
        t: {
          s: 1, // 边框样式
          cl: {
            // 边框颜色
            rgb: "#DEE0E3",
          },
        },
        // 左边框
        r: {
          s: 1, // 边框样式
          cl: {
            // 边框颜色
            rgb: "#DEE0E3",
          },
        },
        // 左边框
        l: {
          s: 1, // 边框样式
          cl: {
            // 边框颜色
            rgb: "#DEE0E3",
          },
        },
      },

      bg: {
        rgb: this._getBackgroundColor(columnConfig, colIndex),
      },
    };

    // 添加下划线（如果需要）
    // if (columnConfig.is_show_modal === 1) {
    //   style.ul = { s: 1 };
    // }

    return style;
  }

  /**
   * 获取文本颜色
   * @param {Object} columnConfig - 列配置
   * @returns {Object} 颜色对象
   * @private
   */
  _getTextColor(columnConfig) {
    // 优先级：自定义颜色 > 下拉选择 > is_show_modal > 默认颜色

    // 下拉选择时显示红色
    if (columnConfig.type === "array") {
      if (columnConfig.is_multiple === 1) {
        return { rgb: "#444444" };
      } else {
        return { rgb: "#444444" };
      }
    }

    if (columnConfig.color) {
      return { rgb: columnConfig.color };
    }

    // if (columnConfig.is_gray === 1) {
    //   return { rgb: "#999999" };
    // }
    if (columnConfig.is_show_modal === 1) {
      return { rgb: "#2E6ED1" };
    }

    return { rgb: "#444444" };
  }

  /**
   * 获取背景颜色
   * @param {Object} columnConfig - 列配置
   * @param {number} colIndex - 列索引
   * @returns {string} 背景颜色
   * @private
   */
  _getBackgroundColor(columnConfig, colIndex) {
    // 第一列设置特定背景颜色
    if (colIndex === 0) {
      return "#F2F4F5";
    }

    // 如果列配置中有自定义背景颜色，使用自定义颜色
    if (columnConfig.bg_color) {
      return columnConfig.bg_color;
    }

    // 如果是灰色列，使用灰色背景
    if (columnConfig.is_gray === 1) {
      return "#F5F7FF";
    }

    // 默认无背景颜色
    return "";
  }

  /**
   * 创建 Univer 工作簿配置
   * @returns {Object} 工作簿配置对象
   */
  createWorkbookConfig(data) {
    const cellData = this.formatCellData(
      data.headers,
      data.cells,
      data.headerDictionaryIndex
    );

    // 工作簿配置
    const workbookConfig = {
      id: "workbook-01",
      name: "工作簿",
      sheets: {
        "sheet-01": {
          cellData: cellData,
          name: "职位检索表格",
          hidden: 0,
          defaultColumnWidth: 200,
          showGridlines: 1,
          gridlinesColor: "#DEE0E3",
          columnCount: Object.keys(cellData[0]).length,
          rowCount: Object.keys(cellData).length,
          // 设置默认样式：所有文字左对齐
          defaultStyle: {
            ht: 1, // 水平对齐：1=左对齐, 2=居中, 3=右对齐
            vt: 2, // 垂直对齐：1=顶部, 2=居中, 3=底部
          },
          freeze: {
            startRow: data.headers.length,
            startColumn: 1,
            ySplit: data.headers.length,
            xSplit: 1,
          },
        },
      },
    };

    return workbookConfig;
  }

  /**
   * 解析工作表数据
   */
  parseSheetData(sheetSnapshot, sourceData) {
    const list = Object.values(sheetSnapshot.cellData);
    const result = { headers: [], cells: [] };

    const map = list[sourceData.headerDictionaryIndex];

    list.forEach((obj, index) => {
      const data = {};
      for (const key in map) {
        const o = obj[key];

        if (o.custom?.job_id) {
          data.job_id = o.custom?.job_id;
        }
        if (o.custom?.id) {
          data.id = o.custom?.id;
        }

        data[map[key].custom.sql_column] = o?.v || "";
      }
      if (index < sourceData.headers.length) {
        result.headers.push(data);
      } else {
        result.cells.push(data);
      }
    });

    return result;
  }
}

/**
 * 权限管理器 - 负责所有权限保护功能的统一管理
 */
class PermissionManager {
  constructor(univerAPI) {
    this.univerAPI = univerAPI;
    this.columnProtectionMap = new Map(); // 存储列权限ID的映射
    this.rowProtectionMap = new Map(); // 存储行权限ID的映射
    this.unifiedProtectionMap = new Map(); // 存储统一保护的映射
  }

  /**
   * 锁定指定列
   * @param {number} columnNumber - 列号（从0开始）
   * @returns {Object|null} 权限信息或null
   */
  async lockColumn(columnNumber) {
    if (!BaseUtils.validateColumnNumber(columnNumber)) {
      return null;
    }

    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return null;
    }

    const workbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = workbook.getActiveSheet();
    const unitId = workbook.getId();
    const subUnitId = fWorksheet.getSheetId();
    const permission = this.univerAPI.getActiveWorkbook().getPermission();
    const range = fWorksheet.getRange(
      0,
      columnNumber,
      fWorksheet.getMaxRows(),
      1
    );
    const ranges = [range];

    const rangeProtectionPermissionEditPoint =
      permission.permissionPointsDefinition.RangeProtectionPermissionEditPoint;
    const res = await permission.addRangeBaseProtection(
      unitId,
      subUnitId,
      ranges
    );

    // 存储权限信息，用于后续解除
    const { permissionId, ruleId } = res;
    this.columnProtectionMap.set(columnNumber, {
      permissionId,
      ruleId,
      unitId,
      subUnitId,
    });

    permission.rangeRuleChangedAfterAuth$.subscribe((currentPermissionId) => {
      if (currentPermissionId === permissionId) {
        // 设置范围保护为不可编辑
        permission.setRangeProtectionPermissionPoint(
          unitId,
          subUnitId,
          permissionId,
          rangeProtectionPermissionEditPoint,
          false
        );
      }
    });
    permission.setPermissionDialogVisible(false);

    return { permissionId, ruleId };
  }

  /**
   * 解锁指定列
   * @param {number} columnNumber - 列号（从0开始）
   * @returns {boolean} 是否解锁成功
   */
  async unlockColumn(columnNumber) {
    const protectionInfo = this.columnProtectionMap.get(columnNumber);

    if (!protectionInfo) {
      return false;
    }

    const { permissionId, ruleId, unitId, subUnitId } = protectionInfo;
    const permission = this.univerAPI.getActiveWorkbook().getPermission();

    try {
      permission.removeRangeProtection(unitId, subUnitId, [ruleId]);

      // 从映射中移除
      this.columnProtectionMap.delete(columnNumber);

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查列是否被锁定
   * @param {number} columnNumber - 列号
   * @returns {boolean} 是否被锁定
   */
  isColumnLocked(columnNumber) {
    return this.columnProtectionMap.has(columnNumber);
  }

  /**
   * 锁定指定行
   * @param {number} rowNumber - 行号（从0开始）
   * @returns {Object|null} 权限信息或null
   */
  async lockRow(rowNumber) {
    if (!BaseUtils.validateRowNumber(rowNumber)) {
      return null;
    }

    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return null;
    }

    const workbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = workbook.getActiveSheet();
    const unitId = workbook.getId();
    const subUnitId = fWorksheet.getSheetId();
    const permission = this.univerAPI.getActiveWorkbook().getPermission();
    const range = fWorksheet.getRange(
      rowNumber,
      0,
      1,
      fWorksheet.getMaxColumns()
    );
    const ranges = [range];

    const rangeProtectionPermissionEditPoint =
      permission.permissionPointsDefinition.RangeProtectionPermissionEditPoint;
    const res = await permission.addRangeBaseProtection(
      unitId,
      subUnitId,
      ranges
    );

    // 存储权限信息，用于后续解除
    const { permissionId, ruleId } = res;
    this.rowProtectionMap.set(rowNumber, {
      permissionId,
      ruleId,
      unitId,
      subUnitId,
    });

    permission.rangeRuleChangedAfterAuth$.subscribe((currentPermissionId) => {
      if (currentPermissionId === permissionId) {
        // 设置范围保护为不可编辑
        permission.setRangeProtectionPermissionPoint(
          unitId,
          subUnitId,
          permissionId,
          rangeProtectionPermissionEditPoint,
          false
        );
      }
    });
    permission.setPermissionDialogVisible(false);

    return { permissionId, ruleId };
  }

  /**
   * 解锁指定行
   * @param {number} rowNumber - 行号（从0开始）
   * @returns {boolean} 是否解锁成功
   */
  async unlockRow(rowNumber) {
    const protectionInfo = this.rowProtectionMap.get(rowNumber);

    if (!protectionInfo) {
      console.warn(`第${rowNumber}行没有被锁定或已经解除锁定`);
      return false;
    }

    const { permissionId, ruleId, unitId, subUnitId } = protectionInfo;
    const permission = this.univerAPI.getActiveWorkbook().getPermission();

    try {
      permission.removeRangeProtection(unitId, subUnitId, [ruleId]);

      // 从映射中移除
      this.rowProtectionMap.delete(rowNumber);

      return true;
    } catch (error) {
      console.error(`解除第${rowNumber}行锁定失败:`, error);
      return false;
    }
  }

  /**
   * 检查行是否被锁定
   * @param {number} rowNumber - 行号
   * @returns {boolean} 是否被锁定
   */
  isRowLocked(rowNumber) {
    return this.rowProtectionMap.has(rowNumber);
  }

  /**
   * 统一保护机制 - 同时处理表头行和特定列的保护
   * @param {Object} config - 保护配置
   * @param {number} config.headerRowCount - 表头行数
   * @param {Array} config.modalColumns - 需要保护的列索引数组
   * @returns {boolean} 是否设置成功
   */
  async setupUnifiedProtection(config) {
    const { headerRowCount, modalColumns = [] } = config;

    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return false;
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      const fWorksheet = workbook.getActiveSheet();
      const unitId = workbook.getId();
      const subUnitId = fWorksheet.getSheetId();
      const permission = workbook.getPermission();
      const maxColumns = fWorksheet.getMaxColumns();

      const ranges = [];

      // 1. 添加表头行保护范围（排除特殊列）
      for (let row = 0; row < headerRowCount; row++) {
        let currentStart = 0;

        for (let col = 0; col <= maxColumns; col++) {
          if (modalColumns.includes(col) || col === maxColumns) {
            // 遇到特殊列或到达末尾，添加之前的范围
            if (currentStart < col) {
              const range = fWorksheet.getRange(
                row,
                currentStart,
                1,
                col - currentStart
              );
              ranges.push(range);
            }
            currentStart = col + 1;
          }
        }
      }

      // 2. 添加特殊列保护范围（整列）
      // for (const columnIndex of modalColumns) {
      //   const range = fWorksheet.getRange(
      //     0,
      //     columnIndex,
      //     fWorksheet.getMaxRows(),
      //     1
      //   );
      //   ranges.push(range);
      // }

      if (ranges.length === 0) {
        return true;
      }

      // 创建统一保护
      const rangeProtectionPermissionEditPoint =
        permission.permissionPointsDefinition
          .RangeProtectionPermissionEditPoint;

      const res = await permission.addRangeBaseProtection(
        unitId,
        subUnitId,
        ranges
      );
      const { permissionId, ruleId } = res;

      // 存储统一保护信息
      const protectionInfo = {
        permissionId,
        ruleId,
        unitId,
        subUnitId,
        type: "unified",
        headerRowCount,
        modalColumns: [...modalColumns],
        ranges: ranges.length,
      };

      this.unifiedProtectionMap.set("main", protectionInfo);

      // 同时在行和列映射中记录（用于兼容性检查）
      for (let rowIndex = 0; rowIndex < headerRowCount; rowIndex++) {
        this.rowProtectionMap.set(rowIndex, {
          ...protectionInfo,
          type: "unified_row",
        });
      }

      for (const columnIndex of modalColumns) {
        this.columnProtectionMap.set(columnIndex, {
          ...protectionInfo,
          type: "unified_column",
        });
      }

      permission.rangeRuleChangedAfterAuth$.subscribe((currentPermissionId) => {
        if (currentPermissionId === permissionId) {
          permission.setRangeProtectionPermissionPoint(
            unitId,
            subUnitId,
            permissionId,
            rangeProtectionPermissionEditPoint,
            false
          );
        }
      });
      permission.setPermissionDialogVisible(false);

      return true;
    } catch (error) {
      console.error("统一保护设置失败:", error);
      return false;
    }
  }

  /**
   * 获取特定类型的列信息
   * @param {Object} sourceData - 源数据
   * @param {string} filterType - 过滤类型 (如 'is_show_modal')
   * @param {*} filterValue - 过滤值
   * @returns {Array} 符合条件的列索引数组
   */
  getColumnsByType(sourceData, filterType, filterValue = 1) {
    if (!sourceData || !sourceData.headers || sourceData.headers.length === 0) {
      return [];
    }

    const headerRow =
      sourceData.headers[sourceData.headerDictionaryIndex] ||
      sourceData.headers[0];
    const columns = [];

    headerRow.forEach((headerConfig, colIndex) => {
      if (headerConfig[filterType] === filterValue) {
        columns.push(colIndex);
      }
    });

    return columns;
  }

  /**
   * 清理所有权限
   */
  clear() {
    this.columnProtectionMap.clear();
    this.rowProtectionMap.clear();
    this.unifiedProtectionMap.clear();
  }
}

/**
 * 表格操作类 - 负责基本的表格数据操作
 */
class SheetOperations {
  constructor(univerAPI, dataConverter, permissionManager) {
    this.univerAPI = univerAPI;
    this.dataConverter = dataConverter;
    this.permissionManager = permissionManager;
    this.sourceData = {};
  }

  /**
   * 设置源数据
   */
  setSourceData(data) {
    this.sourceData = data;
  }

  /**
   * 获取工作表快照
   */
  getSnapshot() {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return null;
    }

    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = fWorkbook.getActiveSheet();
    return fWorksheet.getSheet().getSnapshot();
  }

  /**
   * 获取工作表数据
   */
  getData() {
    const sheetSnapshot = this.getSnapshot();
    if (!sheetSnapshot) {
      return [];
    }

    return this.dataConverter.parseSheetData(sheetSnapshot, this.sourceData);
  }

  /**
   * 获取指定单元格的值
   * @param {number} row - 行索引（从0开始）
   * @param {number} column - 列索引（从0开始）
   * @returns {any} 单元格的值
   */
  getCellValue(row, column) {
    try {
      if (
        !BaseUtils.validateRowNumber(row) ||
        !BaseUtils.validateColumnNumber(column)
      ) {
        return null;
      }

      if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
        return null;
      }

      const fWorkbook = this.univerAPI.getActiveWorkbook();
      const fWorksheet = fWorkbook.getActiveSheet();
      const range = fWorksheet.getRange(row, column, 1, 1);

      return range.getValue();
    } catch (error) {
      console.error("获取单元格值时发生错误:", error);
      return null;
    }
  }

  /**
   * 设置单个单元格的值
   * @param {number} row - 行索引
   * @param {number} column - 列索引
   * @param {any} value - 单元格值
   * @param {Object} options - 选项，包含 custom 等
   * @returns {boolean} 是否设置成功
   */
  async setCellValue(row, column, value, options = {}) {
    row = Number(row);
    column = Number(column);

    try {
      if (
        !BaseUtils.validateRowNumber(row) ||
        !BaseUtils.validateColumnNumber(column)
      ) {
        return false;
      }

      if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
        return false;
      }

      const fWorkbook = this.univerAPI.getActiveWorkbook();
      const fWorksheet = fWorkbook.getActiveSheet();

      // 检查当前列是否被锁定
      const columnNumber = column;
      const isColumnLocked =
        this.permissionManager.isColumnLocked(columnNumber);
      let wasUnlocked = false;

      // 如果列被锁定，先临时解锁
      if (isColumnLocked) {
        const unlockResult = await this.permissionManager.unlockColumn(
          columnNumber
        );
        if (unlockResult) {
          wasUnlocked = true;
        } else {
          console.error(`无法解锁第${columnNumber}列，设置单元格值失败`);
          return false;
        }
      }

      // 获取指定单元格的范围
      const range = fWorksheet.getRange(row, column, 1, 1);

      // 设置单元格值
      range.setValue(value);

      // 如果需要更新自定义数据
      if (options.custom) {
        const sheetSnapshot = fWorksheet.getSheet().getSnapshot();
        if (
          sheetSnapshot.cellData[row] &&
          sheetSnapshot.cellData[row][column]
        ) {
          sheetSnapshot.cellData[row][column].custom = {
            ...sheetSnapshot.cellData[row][column].custom,
            ...options.custom,
          };
        }
      }

      // 如果之前解锁了，现在重新锁定
      if (wasUnlocked) {
        await this.permissionManager.lockColumn(columnNumber);
      }

      return true;
    } catch (error) {
      console.error("设置单元格值时发生错误:", error);
      return false;
    }
  }

  /**
   * 批量修改多个单元格数据 - 优化版本
   * @param {Array} cellUpdates - 单元格更新数组，每个元素包含 {row, column, value, options}
   * @returns {boolean} 是否全部设置成功
   */
  async setCellValues(cellUpdates) {
    try {
      if (!Array.isArray(cellUpdates)) {
        console.error("cellUpdates 必须是数组");
        return false;
      }

      if (cellUpdates.length === 0) {
        return true;
      }

      // 验证输入数据
      const validUpdates = [];
      for (let index = 0; index < cellUpdates.length; index++) {
        const update = cellUpdates[index];
        const { row, column, value, options = {} } = update;

        if (typeof row !== "number" || typeof column !== "number") {
          console.error(`第 ${index} 个更新项的行列值无效:`, update);
          continue;
        }

        if (
          !BaseUtils.validateRowNumber(row) ||
          !BaseUtils.validateColumnNumber(column)
        ) {
          console.error(`第 ${index} 个更新项的行列值超出范围:`, update);
          continue;
        }

        validUpdates.push({ row, column, value, options });
      }

      if (validUpdates.length === 0) {
        console.warn("没有有效的单元格更新");
        return false;
      }

      // 检查API可用性
      if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
        return false;
      }

      const fWorkbook = this.univerAPI.getActiveWorkbook();
      const fWorksheet = fWorkbook.getActiveSheet();

      // 按列分组，批量处理权限
      const columnGroups = new Map();
      validUpdates.forEach((update) => {
        const { column } = update;
        if (!columnGroups.has(column)) {
          columnGroups.set(column, []);
        }
        columnGroups.get(column).push(update);
      });

      // 批量处理列权限
      const unlockedColumns = new Set();
      for (const column of columnGroups.keys()) {
        const isColumnLocked = this.permissionManager.isColumnLocked(column);
        if (isColumnLocked) {
          const unlockResult = await this.permissionManager.unlockColumn(
            column
          );
          if (unlockResult) {
            unlockedColumns.add(column);
          } else {
            console.error(`无法解锁第${column}列，跳过该列的所有更新`);
            // 移除该列的所有更新
            columnGroups.delete(column);
          }
        }
      }

      let allSuccess = true;

      try {
        // 获取工作表快照（如果需要处理custom选项）
        const hasCustomOptions = validUpdates.some(
          (update) => update.options.custom
        );
        let sheetSnapshot = null;
        if (hasCustomOptions) {
          sheetSnapshot = fWorksheet.getSheet().getSnapshot();
        }

        // 批量设置单元格值 - 优化版本
        for (const [column, updates] of columnGroups) {
          try {
            // 尝试使用范围批量设置（如果单元格连续）
            const optimizedRanges = this._optimizeUpdatesIntoRanges(
              updates,
              column
            );

            for (const rangeUpdate of optimizedRanges) {
              if (rangeUpdate.isRange) {
                // 批量设置连续范围
                const range = fWorksheet.getRange(
                  rangeUpdate.startRow,
                  column,
                  rangeUpdate.rowCount,
                  1
                );
                range.setValues(rangeUpdate.values);
              } else {
                // 单个单元格设置
                const { row, value, options } = rangeUpdate;
                const range = fWorksheet.getRange(row, column, 1, 1);
                range.setValue(value);

                // 处理自定义数据
                if (options.custom && sheetSnapshot) {
                  if (
                    sheetSnapshot.cellData[row] &&
                    sheetSnapshot.cellData[row][column]
                  ) {
                    sheetSnapshot.cellData[row][column].custom = {
                      ...sheetSnapshot.cellData[row][column].custom,
                      ...options.custom,
                    };
                  }
                }
              }
            }
          } catch (error) {
            console.error(`设置第${column}列数据时发生错误:`, error);
            allSuccess = false;

            // 如果批量操作失败，回退到逐个设置
            for (const update of updates) {
              const { row, value, options } = update;
              try {
                const range = fWorksheet.getRange(row, column, 1, 1);
                range.setValue(value);

                if (options.custom && sheetSnapshot) {
                  if (
                    sheetSnapshot.cellData[row] &&
                    sheetSnapshot.cellData[row][column]
                  ) {
                    sheetSnapshot.cellData[row][column].custom = {
                      ...sheetSnapshot.cellData[row][column].custom,
                      ...options.custom,
                    };
                  }
                }
              } catch (cellError) {
                console.error(
                  `设置单元格 (${row}, ${column}) 值时发生错误:`,
                  cellError
                );
                allSuccess = false;
              }
            }
          }
        }
      } finally {
        // 重新锁定之前解锁的列
        for (const column of unlockedColumns) {
          try {
            await this.permissionManager.lockColumn(column);
          } catch (error) {
            console.error(`重新锁定第${column}列时发生错误:`, error);
          }
        }
      }

      return allSuccess;
    } catch (error) {
      console.error("批量设置单元格值时发生错误:", error);
      return false;
    }
  }

  /**
   * 优化单元格更新为连续范围操作
   * @param {Array} updates - 单元格更新数组
   * @param {number} column - 列索引
   * @returns {Array} 优化后的范围操作数组
   * @private
   */
  _optimizeUpdatesIntoRanges(updates, column) {
    if (!updates || updates.length === 0) {
      return [];
    }

    // 按行号排序
    const sortedUpdates = [...updates].sort((a, b) => a.row - b.row);
    const optimizedRanges = [];
    let currentRange = null;

    for (const update of sortedUpdates) {
      const { row, value, options } = update;

      // 如果有自定义选项，不能批量处理
      if (options && options.custom) {
        // 先完成当前范围
        if (currentRange) {
          optimizedRanges.push(currentRange);
          currentRange = null;
        }
        // 添加单个单元格操作
        optimizedRanges.push({
          isRange: false,
          row,
          value,
          options,
        });
        continue;
      }

      // 检查是否可以与当前范围合并
      if (
        currentRange &&
        row === currentRange.startRow + currentRange.rowCount
      ) {
        // 连续行，添加到当前范围
        currentRange.values.push([value]);
        currentRange.rowCount++;
      } else {
        // 不连续，先完成当前范围
        if (currentRange) {
          optimizedRanges.push(currentRange);
        }
        // 开始新范围
        currentRange = {
          isRange: true,
          startRow: row,
          rowCount: 1,
          values: [[value]],
        };
      }
    }

    // 完成最后一个范围
    if (currentRange) {
      optimizedRanges.push(currentRange);
    }

    return optimizedRanges;
  }

  /**
   * 批量修改多列数据
   * @param {Array} columnUpdates - 列更新数组，每个元素包含 {column, value, startRow?, endRow?}
   * @returns {boolean} 是否全部设置成功
   */
  async setColumnValues(columnUpdates) {
    try {
      if (!Array.isArray(columnUpdates)) {
        console.error("columnUpdates 必须是数组");
        return false;
      }

      if (columnUpdates.length === 0) {
        console.warn("columnUpdates 数组为空，无需执行任何操作");
        return true;
      }

      // 获取表格信息
      const sheetSnapshot = this.getSnapshot();
      if (!sheetSnapshot) {
        console.error("无法获取工作表快照");
        return false;
      }

      const headerRowCount = BaseUtils.getHeaderRowCount(this.sourceData);
      const totalRowCount = Object.keys(sheetSnapshot.cellData).length;
      const dataRowCount = totalRowCount - headerRowCount;

      if (dataRowCount <= 0) {
        console.warn("没有数据行可以修改");
        return true;
      }

      let allSuccess = true;
      const cellUpdates = [];

      // 遍历每个列更新配置
      for (let i = 0; i < columnUpdates.length; i++) {
        const columnUpdate = columnUpdates[i];
        const { column, value, startRow, endRow } = columnUpdate;

        // 验证列索引
        if (typeof column !== "number" || column < 0) {
          console.error(`第 ${i} 个更新项的列索引无效:`, columnUpdate);
          allSuccess = false;
          continue;
        }

        // 确定行范围
        let actualStartRow = startRow !== undefined ? startRow : headerRowCount;
        let actualEndRow = endRow !== undefined ? endRow : totalRowCount - 1;

        // 验证行范围
        if (actualStartRow < headerRowCount) {
          actualStartRow = headerRowCount;
        }

        if (actualEndRow >= totalRowCount) {
          actualEndRow = totalRowCount - 1;
        }

        if (actualStartRow > actualEndRow) {
          console.error(
            `第 ${i} 个更新项的行范围无效: startRow(${actualStartRow}) > endRow(${actualEndRow})`
          );
          allSuccess = false;
          continue;
        }

        // 生成该列的所有单元格更新
        for (let row = actualStartRow; row <= actualEndRow; row++) {
          cellUpdates.push({
            row: row,
            column: column,
            value: value,
          });
        }
      }

      // 如果有有效的单元格更新，执行批量更新
      if (cellUpdates.length > 0) {
        const batchSuccess = await this.setCellValues(cellUpdates);
        if (!batchSuccess) {
          allSuccess = false;
        }
      }

      return allSuccess;
    } catch (error) {
      console.error("批量修改多列数据时发生错误:", error);
      return false;
    }
  }

  /**
   * 移动列数据到另一列并清空原列
   * @param {number} sourceColumn - 源列索引
   * @param {number} targetColumn - 目标列索引
   * @returns {boolean} 是否移动成功
   */
  async moveColumn(sourceColumn, targetColumn) {
    try {
      // 验证参数
      if (typeof sourceColumn !== "number" || sourceColumn < 0) {
        console.error("源列索引必须是大于等于0的数字");
        return false;
      }

      if (typeof targetColumn !== "number" || targetColumn < 0) {
        console.error("目标列索引必须是大于等于0的数字");
        return false;
      }

      if (sourceColumn === targetColumn) {
        console.error("源列和目标列不能相同");
        return false;
      }

      // 获取表格信息
      const sheetSnapshot = this.getSnapshot();
      if (!sheetSnapshot) {
        console.error("无法获取工作表快照");
        return false;
      }

      const headerRowCount = BaseUtils.getHeaderRowCount(this.sourceData);
      const totalRowCount = Object.keys(sheetSnapshot.cellData).length;

      // 确定行范围：从表头行之后开始到最后一行
      const actualStartRow = headerRowCount;
      const actualEndRow = totalRowCount - 1;

      if (actualStartRow > actualEndRow) {
        console.warn("没有数据行需要移动");
        return true;
      }

      // 收集源列的数据
      const sourceData = [];
      for (let row = actualStartRow; row <= actualEndRow; row++) {
        const cellData = sheetSnapshot.cellData[row]?.[sourceColumn];
        sourceData.push({
          row: row,
          value: cellData?.v || "",
        });
      }

      // 准备批量更新操作
      const cellUpdates = [];

      // 1. 将源列数据写入目标列
      sourceData.forEach(({ row, value }) => {
        cellUpdates.push({
          row: row,
          column: targetColumn,
          value: value,
        });
      });

      // 2. 清空源列数据
      sourceData.forEach(({ row }) => {
        cellUpdates.push({
          row: row,
          column: sourceColumn,
          value: "",
        });
      });

      // 执行批量更新
      const success = await this.setCellValues(cellUpdates);

      return success;
    } catch (error) {
      console.error("移动列数据时发生错误:", error);
      return false;
    }
  }

  /**
   * 获取工作表选择区域数据（过滤掉被筛选隐藏的行）
   */
  getSelection() {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return [];
    }

    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = fWorkbook.getActiveSheet();
    const fSelection = fWorksheet.getSelection();
    const rangeList = fSelection.getActiveRangeList();

    // 如果没有选择区域或只选择了单个默认单元格，返回空数组
    if (!rangeList || rangeList.length === 0) {
      return [];
    }

    // 获取筛选器和被隐藏的行
    const filter = fWorksheet.getFilter();
    let filteredOutRows = new Set();

    if (filter) {
      try {
        const hiddenRows = filter.getFilteredOutRows();
        if (hiddenRows && Array.isArray(hiddenRows)) {
          filteredOutRows = new Set(hiddenRows);
        }
      } catch (error) {
        console.warn("获取筛选隐藏行时出错:", error);
      }
    }

    // 使用 Map 来按列索引合并数据
    const columnDataMap = new Map();

    rangeList.forEach((range) => {
      const startColumn = range.getColumn();
      const startRow = range.getRow();
      const cellDatas = range.getCellDatas();

      // 跳过默认的单个单元格选择
      if (startColumn === 0 && startRow === 0 && cellDatas?.length === 1) {
        return;
      }

      // 按列维度整理数据，传入筛选隐藏行信息
      const columnOrganizedData = this.organizeDataByColumns(
        cellDatas,
        startRow,
        startColumn,
        filteredOutRows
      );

      // 将当前范围的数据合并到 Map 中，相同列的数据会被合并
      columnOrganizedData.forEach((columnData) => {
        const columnIndex = columnData.column;

        if (columnDataMap.has(columnIndex)) {
          // 如果该列已存在，合并 list 数据
          const existingColumnData = columnDataMap.get(columnIndex);
          existingColumnData.list.push(...columnData.list);
        } else {
          // 如果该列不存在，直接添加
          columnDataMap.set(columnIndex, columnData);
        }
      });
    });

    // 将 Map 转换为数组并按列索引排序
    const result = Array.from(columnDataMap.values());
    result.sort((a, b) => a.column - b.column);

    return result;
  }

  /**
   * 按列维度整理数据
   * @param {Object} cellDatas - 原始单元格数据
   * @param {number} startRow - 起始行
   * @param {number} startColumn - 起始列
   * @param {Set} filteredOutRows - 被筛选隐藏的行集合（可选）
   * @returns {Array} 按列整理的数据数组
   */
  organizeDataByColumns(
    cellDatas,
    startRow,
    startColumn,
    filteredOutRows = new Set()
  ) {
    const result = [];

    // 获取表头信息（第0行）
    const sheetSnapshot = this.getSnapshot();
    const headerRow =
      sheetSnapshot.cellData[this.sourceData.headerDictionaryIndex];

    if (!headerRow) {
      console.error("无法获取表头数据");
      return result;
    }

    // 获取表头行数，用于判断是否为表头行
    const headerRowCount = BaseUtils.getHeaderRowCount(this.sourceData);

    // 遍历选中区域的每一列
    Object.keys(cellDatas).forEach((rowKey) => {
      const rowData = cellDatas[rowKey];

      Object.keys(rowData).forEach((colKey) => {
        const absoluteColumn = startColumn + parseInt(colKey);
        const absoluteRow = startRow + parseInt(rowKey);

        // 获取对应的表头信息
        const headerCell = headerRow[absoluteColumn];
        if (!headerCell) return;

        // 查找是否已存在该列的数据对象
        let columnDataObj = result.find(
          (item) => item.column === absoluteColumn
        );

        if (!columnDataObj) {
          // 创建新的列数据对象
          columnDataObj = {
            column: absoluteColumn,
            columnName: headerCell.v || `列${absoluteColumn}`,
            sql_column: headerCell.custom?.sql_column || "",
            type: headerCell.custom?.type || "string",
            enum: headerCell.custom?.enum || [],
            description: headerCell.custom?.description || "",
            list: [],
          };
          result.push(columnDataObj);
        }

        // 判断是否为表头行：如果是表头行，不添加数据到list中
        if (absoluteRow < headerRowCount) {
          // 表头行：不添加任何数据到list，保持list为空数组
          return;
        }

        // 检查该行是否被筛选器隐藏
        if (filteredOutRows.has(absoluteRow)) {
          // 该行被筛选隐藏，跳过不添加到结果中
          // console.log(`跳过被筛选隐藏的行: ${absoluteRow}`);
          return;
        }

        // 数据行：添加单元格数据到列表中
        const cellData = rowData[colKey];
        columnDataObj.list.push({
          column: absoluteColumn,
          row: absoluteRow,
          value: cellData?.v || "",
          custom: cellData?.custom || {},
        });
      });
    });

    // 按列索引排序
    result.sort((a, b) => a.column - b.column);
    return result;
  }

  /**
   * 获取筛选器状态信息
   * @returns {Object} 筛选器状态信息
   */
  getFilterStatus() {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return { hasFilter: false, filteredOutRows: [], visibleRowCount: 0 };
    }

    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = fWorkbook.getActiveSheet();
    const filter = fWorksheet.getFilter();

    if (!filter) {
      return { hasFilter: false, filteredOutRows: [], visibleRowCount: 0 };
    }

    try {
      const filteredOutRows = filter.getFilteredOutRows() || [];
      const sheetSnapshot = this.getSnapshot();
      const totalRows = sheetSnapshot ? sheetSnapshot.rowCount : 0;
      const headerRowCount = BaseUtils.getHeaderRowCount(this.sourceData);
      const dataRowCount = Math.max(0, totalRows - headerRowCount);
      const visibleRowCount = Math.max(
        0,
        dataRowCount - filteredOutRows.length
      );

      return {
        hasFilter: true,
        filteredOutRows: filteredOutRows,
        totalRows: totalRows,
        dataRowCount: dataRowCount,
        visibleRowCount: visibleRowCount,
        hiddenRowCount: filteredOutRows.length,
      };
    } catch (error) {
      console.warn("获取筛选器状态时出错:", error);
      return { hasFilter: true, filteredOutRows: [], visibleRowCount: 0 };
    }
  }

  /**
   * 结束工作簿编辑状态
   * 调用 univerAPI 的 endEditingAsync 方法来结束当前工作簿的编辑状态
   * @param {boolean} saveChanges - 是否保存更改，默认为 true
   * @returns {Promise<boolean>} 是否成功结束编辑状态
   */
  async endWorkbookEditing() {
    let saveChanges = true;
    try {
      if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
        console.error("univerAPI 未初始化，无法结束编辑状态");
        return false;
      }

      const fWorkbook = this.univerAPI.getActiveWorkbook();
      if (!fWorkbook) {
        console.error("无法获取活动工作簿");
        return false;
      }

      // 调用 endEditingAsync 方法
      await fWorkbook.endEditingAsync(saveChanges);

      return true;
    } catch (error) {
      console.error("结束工作簿编辑状态时发生错误:", error);
      return false;
    }
  }
}

/**
 * 事件管理器 - 负责事件处理和回调管理
 */
class EventManager {
  constructor(univerAPI) {
    this.univerAPI = univerAPI;
    this.eventCallbacks = new Map();
    // 双击检测相关属性
    this.lastClickTime = 0;
    this.lastClickCell = null;
    this.doubleClickDelay = 300; // 双击间隔时间（毫秒）
  }

  /**
   * 从表格API获取列配置信息
   * @param {number} column - 列索引
   * @param {number} headerRowIndex - 表头行索引，默认为0
   * @returns {Object|null} 列配置对象
   */
  getColumnConfig(column, headerRowIndex = 0) {
    try {
      if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
        return null;
      }

      const fWorkbook = this.univerAPI.getActiveWorkbook();
      const fWorksheet = fWorkbook.getActiveSheet();
      const sheetSnapshot = fWorksheet.getSheet().getSnapshot();

      // 获取表头行数据
      const headerRow = sheetSnapshot.cellData[headerRowIndex];
      if (!headerRow || !headerRow[column]) {
        return null;
      }

      // 返回存储在 custom 属性中的列配置
      return headerRow[column].custom || null;
    } catch (error) {
      console.error("获取列配置时发生错误:", error);
      return null;
    }
  }

  /**
   * 设置命令绑定
   */
  setupCommandBinding() {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return;
    }

    this.univerAPI.onBeforeCommandExecute((command) => {
      return this.handleCellEdit(command);
    });

    univerAPI.addEvent(univerAPI.Event.BeforeSheetEditStart, (params) => {
      const { column, eventType } = params;

      // 直接从表格API获取列配置
      const columnConfig = this.getColumnConfig(column);

      // 如果列配置存在且 is_show_modal = 1，则不允许编辑
      if (columnConfig && columnConfig.is_show_modal === 1) {
        params.cancel = true;
        return;
      }
    });

    this.univerAPI.addEvent(
      this.univerAPI.Event.CellPointerDown,
      async (params) => {
        const { row, column } = params;
        const currentTime = Date.now();
        const currentCell = `${row}-${column}`;

        // 检查是否为双击同一个单元格
        const isDoubleClick =
          this.lastClickCell === currentCell &&
          currentTime - this.lastClickTime <= this.doubleClickDelay;

        // 更新点击记录
        this.lastClickTime = currentTime;
        this.lastClickCell = currentCell;

        // 只有双击同一单元格时才触发回调
        if (!isDoubleClick) {
          return;
        }

        const fWorkbook = this.univerAPI.getActiveWorkbook();
        const fWorksheet = fWorkbook.getActiveSheet();

        // 获取工作表快照以构造 cell 对象
        const sheetSnapshot = fWorksheet.getSheet().getSnapshot();
        const actualRow = row;
        const actualColumn = column;

        // 构造与 activateCellEdit 一致的 cell 对象
        const headerColumn = sheetSnapshot.cellData[0]?.[actualColumn];
        const obj = sheetSnapshot.cellData[actualRow]?.[actualColumn];

        if (headerColumn && obj) {
          const cell = JSON.parse(
            JSON.stringify({
              ...obj,
              ...obj.custom,
              custom: headerColumn.custom,
              column: actualColumn,
              row: actualRow,
            })
          );

          // 触发自定义事件回调
          const callback = this.eventCallbacks.get("activateCellEdit");
          if (callback) {
            callback({ cell });
          }
        }
      }
    );
  }

  /**
   * 处理单元格编辑命令
   * @param {Object} command - 命令对象
   * @returns {boolean} 是否允许执行命令
   */
  handleCellEdit(command) {
    // if (command.id === "sheet.operation.set-activate-cell-edit") {
    //   const params = command?.params?.primary;

    //   if (!params) return true; // 允许执行

    //   const fWorkbook = this.univerAPI.getActiveWorkbook();
    //   const fWorksheet = fWorkbook.getActiveSheet();

    //   const sheetSnapshot = fWorksheet.getSheet().getSnapshot();
    //   const { actualColumn, actualRow } = params;

    //   const headerColumn = sheetSnapshot.cellData[0][actualColumn];
    //   const obj = sheetSnapshot.cellData[actualRow][actualColumn];
    //   const cell = JSON.parse(
    //     JSON.stringify({
    //       ...obj,
    //       ...obj.custom,
    //       custom: headerColumn.custom,
    //       column: actualColumn,
    //       row: actualRow,
    //     })
    //   );

    //   // 触发自定义事件回调
    //   const callback = this.eventCallbacks.get("activateCellEdit");
    //   if (callback) {
    //     callback({ cell });
    //   }
    // }

    return true; // 允许执行命令
  }

  /**
   * 注册事件回调
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  onCellActivate(callback) {
    this.eventCallbacks.set("activateCellEdit", callback);
  }

  /**
   * 清理事件回调
   */
  clear() {
    this.eventCallbacks.clear();
    // 重置双击检测状态
    this.lastClickTime = 0;
    this.lastClickCell = null;
  }
}

/**
 * UI管理器 - 负责用户界面配置和交互（不包括权限管理）
 */
class UIManager {
  constructor(univerAPI) {
    this.univerAPI = univerAPI;
  }

  /**
   * 设置源数据
   */
  setSourceData(data) {
    this.sourceData = data;
  }

  /**
   * 设置下拉验证
   */
  setupDropdownValidation() {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return;
    }

    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = fWorkbook.getActiveSheet();

    // 从表格表头的 custom 字段中读取配置
    const sheetSnapshot = fWorksheet.getSheet().getSnapshot();
    const headerRow =
      sheetSnapshot.cellData[this.sourceData.headerDictionaryIndex]; // 第一行是表头

    if (!headerRow) {
      console.error("无法获取表头数据");
      return;
    }

    // 遍历表头列，为不同类型的字段设置相应的验证规则
    Object.keys(headerRow).forEach((column) => {
      const headerCell = headerRow[column];
      const config = headerCell.custom; // 从 custom 字段获取配置

      if (!config) return;

      const colIndex = parseInt(column);
      // 动态获取表格总行数
      const totalRowCount = fWorksheet.getMaxRows();
      const range = fWorksheet.getRange(
        this.sourceData.headers.length,
        colIndex,
        totalRowCount - this.sourceData.headers.length,
        1
      ); // 减去表头行

      // 处理 array 类型：设置多选下拉
      if (config.type === "array" && config.enum && config.enum.length > 0) {
        // 提取选项值
        const options = config.enum.map((item) => {
          // 如果 enum 项是对象，取 name 字段；如果是字符串，直接使用
          return typeof item === "object" ? item.name : item;
        });
        // 方案1：不为空值设置颜色，只为有值的选项设置颜色
        const multipleColors = [
          "#FFE6E6",
          "#EFCCFF",
          "#FFDCBD",
          "#C7ECFF",
          "#C7DBFF",
          "#D5F0E2",
          "#D5F0E2",
        ];
        const singleColors = options.map((item) => "#F5F7FF");
        const isMultiple = Number(config.is_multiple) === 1;
        const colors = isMultiple ? multipleColors : singleColors;
        // 创建多选验证规则
        const multiSelectRule = this.univerAPI
          .newDataValidation()
          .requireValueInList(options, isMultiple, true) // true = 多选, true = 显示下拉
          .setAllowBlank(true)

          .setOptions({
            showErrorMessage: true,
            error: `请从列表中选择${config.name}`,
            // 根据单选/多选设置不同的渲染模式
            renderMode: isMultiple
              ? this.univerAPI.Enum.DataValidationRenderMode.CUSTOM // 多选：条状标签
              : this.univerAPI.Enum.DataValidationRenderMode.ARROW, // 单选：箭头
          })
          .build();

        range.setDataValidation(multiSelectRule);
        const appliedRule = range.getDataValidation();
        if (appliedRule) {
          // 使用 setCriteria 方法设置颜色信息

          appliedRule.setCriteria(
            this.univerAPI.Enum.DataValidationType.LIST_MULTIPLE, // 或 LIST
            [
              undefined, // operator (列表类型不需要)
              options.join(","), // formula1: 选项列表
              colors.join(","), // formula2: 颜色列表
            ],
            true // allowBlank
          );
        }
      }

      // 处理 date 类型：设置日期验证
      else if (config.type === "date") {
        // 创建日期验证规则
        const dateRule = this.univerAPI
          .newDataValidation()
          .requireDateAfter(new Date("2000-01-01")) // 2024年1月1日之后
          .setAllowBlank(false)
          .setOptions({
            showErrorMessage: true,
            error: `请输入有效的日期格式`,
          })
          .build();

        range.setDataValidation(dateRule);
      }
    });
  }

  /**
   * 初始化完成后立即设置筛选器
   * 为整个数据表格启用筛选功能，用户可以对任意列进行筛选操作
   */
  setupFilter() {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return;
    }

    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = fWorkbook.getActiveSheet();
    const sheetSnapshot = fWorksheet.getSheet().getSnapshot();

    try {
      const filterRange = fWorksheet.getRange(
        this.sourceData.headerDictionaryIndex,
        0,
        sheetSnapshot.rowCount,
        sheetSnapshot.columnCount
      );

      // 检查是否已存在筛选器，如果存在则先移除
      const existingFilter = fWorksheet.getFilter();
      if (existingFilter) {
        existingFilter.remove();
      }

      // 创建新的筛选器
      const filter = filterRange.createFilter();
      this.setupFilterEventListeners();

      if (!filter) {
        console.error("筛选器创建失败");
      }
    } catch (error) {
      console.error("设置筛选器时发生错误：", error);
    }
  }

  /**
   * 为表头单元格设置备注
   * 在初始化完成后调用，为有 remark 字段的表头单元格添加备注
   */
  setupHeaderNotes() {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return;
    }

    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = fWorkbook.getActiveSheet();

    // 遍历所有表头行
    this.sourceData.headers.forEach((headerRow, rowIndex) => {
      headerRow.forEach((headerConfig, colIndex) => {
        // 检查是否有 remark 字段且不为空
        if (headerConfig.remark && headerConfig.remark.trim() !== "") {
          try {
            // 获取单元格范围
            const fRange = fWorksheet.getRange(rowIndex, colIndex, 1, 1);

            // 创建或更新备注
            fRange.createOrUpdateNote({
              note: headerConfig.remark,
              width: 200,
              height: 28,
              // 添加更多配置选项确保备注正确显示
              autoSize: false,
              visible: true,
            });
          } catch (error) {
            console.error(
              `设置表头备注失败 [${rowIndex}, ${colIndex}]:`,
              error
            );
          }
        }
      });
    });
  }

  /**
   * 设置筛选器事件监听
   * 监听筛选操作，提供用户反馈
   */
  setupFilterEventListeners() {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return;
    }

    // 监听筛选操作完成事件
    const filterDisposable = this.univerAPI.addEvent(
      this.univerAPI.Event.SheetRangeFiltered,
      (params) => {
        const { worksheet } = params;

        // 获取筛选后被隐藏的行数
        const filter = worksheet.getFilter();
        if (filter) {
          filter.getFilteredOutRows();
        }
      }
    );

    // 监听筛选器清除事件
    const clearDisposable = this.univerAPI.addEvent(
      this.univerAPI.Event.SheetRangeFilterCleared,
      () => {
        // 筛选器已清除，显示所有数据
      }
    );

    // 监听筛选前事件（可用于验证或阻止某些筛选操作）
    const beforeFilterDisposable = this.univerAPI.addEvent(
      this.univerAPI.Event.SheetBeforeRangeFilter,
      () => {
        // 这里可以添加筛选前的验证逻辑
        // 如果需要阻止筛选，可以设置 params.cancel = true;
      }
    );

    // 返回清理函数（可选）
    return () => {
      filterDisposable.dispose();
      clearDisposable.dispose();
      beforeFilterDisposable.dispose();
    };
  }
}

/**
 * 主管理器 - 协调各个子模块，提供统一的外部接口
 */
class JobTableManager {
  constructor({ univerAPI, univer }) {
    this.univerAPI = univerAPI;
    this.univer = univer;

    // 初始化各个子模块
    this.dataConverter = new DataConverter();
    this.permissionManager = new PermissionManager(univerAPI);
    this.sheetOperations = new SheetOperations(
      univerAPI,
      this.dataConverter,
      this.permissionManager
    );
    this.eventManager = new EventManager(univerAPI);
    this.uiManager = new UIManager(univerAPI); // 移除 permissionManager 依赖
  }

  /**
   * 初始化应用
   * @param {Object} data - 初始化数据
   */
  initializeTable(data) {
    // 设置事件回调
    if (data.sheetCallEvent && data.sheetCallEvent.activateCellEdit) {
      this.eventManager.onCellActivate(data.sheetCallEvent.activateCellEdit);
    }

    // 设置源数据
    this.sheetOperations.setSourceData(data);
    this.uiManager.setSourceData(data);
    // EventManager 不再需要源数据，直接从表格API获取

    // 创建工作簿
    const workbookData = this.dataConverter.createWorkbookConfig(data);
    this.univerAPI.createWorkbook(workbookData);

    // 设置各种功能
    setTimeout(async () => {
      setTimeout(() => {
        this.uiManager.setupFilter();
      }, 1000);
      this.uiManager.setupDropdownValidation();
      // this.uiManager.setupHeaderNotes();
      this.eventManager.setupCommandBinding();

      // 设置自定义剪贴板行为
      this._setupClipboardBehavior();

      // 设置列宽度（支持自定义宽度类型）
      this._setupColumnWidths(data);

      // 使用重构后的统一保护机制
      // 获取表头行数
      const headerRowCount = BaseUtils.getHeaderRowCount(data);

      // 设置统一保护
      const success = await this.permissionManager.setupUnifiedProtection({
        headerRowCount,
        modalColumns: [],
      });

      if (!success) {
        console.error("统一保护机制设置失败");
      }
    }, 1000);
  }

  /**
   * 设置自定义剪贴板行为
   * 只粘贴文本内容，格式使用当前单元格样式
   * @private
   */
  _setupClipboardBehavior() {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return;
    }

    try {
      // 获取剪贴板服务
      const clipboardService = this.univerAPI._injector.get(
        this.univerAPI._injector.get("ISheetClipboardService")
      );

      if (clipboardService && clipboardService.addClipboardHook) {
        // 添加自定义剪贴板钩子
        clipboardService.addClipboardHook({
          id: "job-table-paste-value-only",
          priority: 1000, // 高优先级，确保优先执行

          onPasteCells: (pasteFrom, pasteTo, data, payload) => {
            // 只处理普通粘贴，不处理特殊粘贴
            if (payload.pasteType !== "default-paste") {
              return { undos: [], redos: [] };
            }

            const mutations = [];
            const undoMutations = [];

            // 遍历粘贴数据，只提取文本值
            data.forValue((row, col, cellData) => {
              if (cellData && cellData.v !== undefined) {
                const targetRow = pasteTo.range.startRow + row;
                const targetCol = pasteTo.range.startColumn + col;

                // 创建只包含值的单元格数据，不包含样式
                const newCellData = {
                  v: cellData.v, // 只保留值
                  // 不包含样式信息 (s)，让目标单元格保持原有样式
                };

                mutations.push({
                  id: "sheet.mutation.set-range-values",
                  params: {
                    unitId: pasteTo.unitId,
                    subUnitId: pasteTo.subUnitId,
                    cellValue: {
                      [targetRow]: {
                        [targetCol]: newCellData,
                      },
                    },
                  },
                });

                // 创建撤销操作
                undoMutations.push({
                  id: "sheet.mutation.set-range-values",
                  params: {
                    unitId: pasteTo.unitId,
                    subUnitId: pasteTo.subUnitId,
                    cellValue: {
                      [targetRow]: {
                        [targetCol]: null, // 撤销时清空值
                      },
                    },
                  },
                });
              }
            });

            return {
              redos: mutations,
              undos: undoMutations,
            };
          },
        });

        console.log("自定义剪贴板行为设置成功：只粘贴文本内容");
      }
    } catch (error) {
      console.error("设置自定义剪贴板行为失败:", error);
    }
  }

  /**
   * 设置列宽度（支持自定义宽度类型）
   * @param {Object} data - 源数据
   * @private
   */
  _setupColumnWidths(data) {
    if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
      return;
    }

    try {
      const fWorkbook = this.univerAPI.getActiveWorkbook();
      const fWorksheet = fWorkbook.getActiveSheet();

      // 获取表头配置行
      const headerConfig = data.headers[data.headerDictionaryIndex];

      if (!headerConfig) {
        console.warn("无法获取表头配置，跳过列宽度设置");
        return;
      }

      // 定义宽度类型映射
      const widthTypeMap = {
        short: 120,
        medium: 200,
        long: 300,
      };

      // 遍历每一列，根据配置设置列宽度
      headerConfig.forEach((columnConfig, colIndex) => {
        let targetWidth = null;

        // 优先检查新的 width_type 字段
        if (columnConfig.width_type && widthTypeMap[columnConfig.width_type]) {
          targetWidth = widthTypeMap[columnConfig.width_type];
        }
        // 向后兼容：如果没有 width_type 字段，继续使用原有的多选列逻辑
        else if (
          columnConfig.type === "array" &&
          Number(columnConfig.is_multiple) === 1
        ) {
          targetWidth = 300; // 保持原有的多选列宽度
        }

        // 设置列宽度
        if (targetWidth !== null) {
          try {
            fWorksheet.setColumnWidth(colIndex, targetWidth);
          } catch (error) {
            console.error(`设置列 ${colIndex} 宽度失败:`, error);
          }
        }
      });
    } catch (error) {
      console.error("设置列宽度时发生错误:", error);
    }
  }

  // === 数据操作方法 ===
  getSnapshot() {
    return this.sheetOperations.getSnapshot();
  }

  getData() {
    return this.sheetOperations.getData();
  }

  getSelection() {
    return this.sheetOperations.getSelection();
  }

  getFilterStatus() {
    return this.sheetOperations.getFilterStatus();
  }

  getCellValue(row, column) {
    return this.sheetOperations.getCellValue(row, column);
  }

  async setCellValue(row, column, value, options = {}) {
    return await this.sheetOperations.setCellValue(row, column, value, options);
  }

  async setCellValues(cellUpdates) {
    return await this.sheetOperations.setCellValues(cellUpdates);
  }

  async setColumnValues(columnUpdates) {
    return await this.sheetOperations.setColumnValues(columnUpdates);
  }

  async moveColumn(sourceColumn, targetColumn) {
    return await this.sheetOperations.moveColumn(sourceColumn, targetColumn);
  }

  async endWorkbookEditing(saveChanges = true) {
    return await this.sheetOperations.endWorkbookEditing(saveChanges);
  }

  // === 权限管理方法 ===
  async lockColumn(columnNumber) {
    return await this.permissionManager.lockColumn(columnNumber);
  }

  async unlockColumn(columnNumber) {
    return await this.permissionManager.unlockColumn(columnNumber);
  }

  isColumnLocked(columnNumber) {
    return this.permissionManager.isColumnLocked(columnNumber);
  }

  async lockRow(rowNumber) {
    return await this.permissionManager.lockRow(rowNumber);
  }

  async unlockRow(rowNumber) {
    return await this.permissionManager.unlockRow(rowNumber);
  }

  isRowLocked(rowNumber) {
    return this.permissionManager.isRowLocked(rowNumber);
  }

  // === UI 操作方法 ===
  /**
   * 显示查找对话框
   * @returns {boolean} 是否成功打开查找对话框
   */
  showFindDialog() {
    try {
      if (!BaseUtils.validateUniverAPI(this.univerAPI)) {
        console.error("univerAPI 未初始化，无法打开查找对话框");
        return false;
      }

      // 尝试执行查找对话框命令
      const possibleCommandIds = ["ui.operation.open-replace-dialog"];

      // 尝试执行可能的命令ID
      for (const commandId of possibleCommandIds) {
        try {
          this.univerAPI.executeCommand(commandId);
          return true;
        } catch (error) {
          console.warn(`命令 ${commandId} 执行失败:`, error.message);
          continue;
        }
      }

      // 如果所有预定义的命令都失败，尝试通过快捷键模拟
      return this._triggerFindShortcut();
    } catch (error) {
      console.error("显示查找对话框时发生错误:", error);
      return false;
    }
  }

  /**
   * 通过快捷键模拟触发查找功能
   * @private
   */
  _triggerFindShortcut() {
    try {
      // 创建 Ctrl+F 键盘事件
      const event = new KeyboardEvent("keydown", {
        key: "f",
        code: "KeyF",
        ctrlKey: true,
        bubbles: true,
        cancelable: true,
      });

      // 尝试在文档上触发事件
      document.dispatchEvent(event);

      // 也尝试在 Univer 容器上触发事件
      const univerContainer =
        document.querySelector(".univer-container") ||
        document.querySelector("[data-univer-container]") ||
        document.body;

      if (univerContainer) {
        univerContainer.dispatchEvent(event);
      }

      return true;
    } catch (error) {
      console.error("触发查找快捷键时发生错误:", error);
      return false;
    }
  }

  /**
   * 销毁实例，清理资源
   */
  destroy() {
    this.permissionManager.clear();
    this.eventManager.clear();
    this.univerAPI = null;
    this.univer = null;
  }
}

/**
 * 向后兼容的JobTableUniver类
 * 保持原有的API接口不变，内部使用新的架构
 */
class JobTableUniver {
  constructor({ univerAPI, univer }) {
    // 使用新的管理器
    this.manager = new JobTableManager({ univerAPI, univer });

    // 保持向后兼容
    this.univerAPI = univerAPI;
    this.univer = univer;
    this.columnProtectionMap =
      this.manager.permissionManager.columnProtectionMap;
    this.sourceData = {};
  }

  // === 保持原有方法名的兼容性 ===
  initUniverApp(data) {
    this.sourceData = data;
    return this.manager.initializeTable(data);
  }

  createUniverWorkbook(data) {
    return this.manager.dataConverter.createWorkbookConfig(data);
  }

  formatCellData(headers, cells, headerDictionaryIndex) {
    return this.manager.dataConverter.formatCellData(
      headers,
      cells,
      headerDictionaryIndex
    );
  }

  getSheetSnapshot() {
    return this.manager.getSnapshot();
  }

  getSheetData() {
    return this.manager.getData();
  }

  getSheetSelection() {
    return this.manager.getSelection();
  }

  getSheetFilterStatus() {
    return this.manager.getFilterStatus();
  }

  getSheetCellValue(row, column) {
    return this.manager.getCellValue(row, column);
  }

  async setSheetCellValue(row, column, value, options = {}) {
    return await this.manager.setCellValue(row, column, value, options);
  }

  async setSheetCellValues(cellUpdates) {
    return await this.manager.setCellValues(cellUpdates);
  }

  async setSheetMultipleColumns(columnUpdates) {
    return await this.manager.setColumnValues(columnUpdates);
  }

  async moveSheetColumn(sourceColumn, targetColumn) {
    return await this.manager.moveColumn(sourceColumn, targetColumn);
  }

  async endWorkbookEditing(saveChanges = true) {
    return await this.manager.endWorkbookEditing(saveChanges);
  }

  async lockColumn(columnNumber) {
    return await this.manager.lockColumn(columnNumber);
  }

  async unlockColumn(columnNumber) {
    return await this.manager.unlockColumn(columnNumber);
  }

  async lockRow(rowNumber) {
    return await this.manager.lockRow(rowNumber);
  }

  async unlockRow(rowNumber) {
    return await this.manager.unlockRow(rowNumber);
  }

  isRowLocked(rowNumber) {
    return this.manager.isRowLocked(rowNumber);
  }

  setupDropdownValidationByAPI() {
    return this.manager.uiManager.setupDropdownValidation();
  }

  setupFilterOnInit() {
    return this.manager.uiManager.setupFilter();
  }

  setupCommandBind() {
    return this.manager.eventManager.setupCommandBinding();
  }

  handleCommandCellEdit(command) {
    return this.manager.eventManager.handleCellEdit(command);
  }

  /**
   * 锁定表头行
   * 向后兼容方法，调用重构后的权限管理功能
   * @returns {Promise} 锁定操作的Promise
   */
  async lockHeaderRows() {
    if (!this.sourceData || !this.sourceData.headers) {
      console.error("无法获取表头数据，无法锁定表头行");
      return false;
    }

    const headerRowCount = BaseUtils.getHeaderRowCount(this.sourceData);
    const modalColumns = this.manager.permissionManager.getColumnsByType(
      this.sourceData,
      "is_show_modal",
      1
    );

    return await this.manager.permissionManager.setupUnifiedProtection({
      headerRowCount,
      modalColumns,
    });
  }

  /**
   * 显示查找对话框
   * 暴露出显示查找API，核心实现是调用 ui.operation.open-find-dialog 命令
   * @returns {boolean} 是否成功打开查找对话框
   */
  showSheetFindDialog() {
    return this.manager.showFindDialog();
  }

  destroy() {
    return this.manager.destroy();
  }
}
