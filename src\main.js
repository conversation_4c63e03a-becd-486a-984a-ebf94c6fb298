import {
  LocaleType,
  mergeLocales,
  Univer,
  UniverInstanceType,
} from "@univerjs/core";
import { FUniver } from "@univerjs/core/facade";
import DesignZhCN from "@univerjs/design/locale/zh-CN";
import { UniverDocsPlugin } from "@univerjs/docs";
import { UniverDocsUIPlugin } from "@univerjs/docs-ui";
import DocsUIZhCN from "@univerjs/docs-ui/locale/zh-CN";
import { UniverFormulaEnginePlugin } from "@univerjs/engine-formula";
import { UniverRenderEnginePlugin } from "@univerjs/engine-render";
import { UniverSheetsPlugin } from "@univerjs/sheets";
import { UniverSheetsFormulaPlugin } from "@univerjs/sheets-formula";
import { UniverSheetsFormulaUIPlugin } from "@univerjs/sheets-formula-ui";
import SheetsF<PERSON>ulaUIZhCN from "@univerjs/sheets-formula-ui/locale/zh-CN";
import { UniverSheetsNumfmtPlugin } from "@univerjs/sheets-numfmt";
import { UniverSheetsNumfmtUIPlugin } from "@univerjs/sheets-numfmt-ui";
import SheetsNumfmtUIZhCN from "@univerjs/sheets-numfmt-ui/locale/zh-CN";
import { UniverSheetsUIPlugin } from "@univerjs/sheets-ui";
import SheetsUIZhCN from "@univerjs/sheets-ui/locale/zh-CN";
import SheetsZhCN from "@univerjs/sheets/locale/zh-CN";
import { UniverUIPlugin } from "@univerjs/ui";
import UIZhCN from "@univerjs/ui/locale/zh-CN";

import { UniverDataValidationPlugin } from "@univerjs/data-validation";
import { UniverSheetsDataValidationPlugin } from "@univerjs/sheets-data-validation";
import { UniverSheetsDataValidationUIPlugin } from "./plugins/sheets-data-validation-ui/lib/index";
import SheetsDataValidationZhCN from "@univerjs/sheets-data-validation-ui/locale/zh-CN";
import "./css/sheets-data-validation-ui/index.css";
import "@univerjs/sheets-data-validation/facade";

import { UniverSheetsFilterPlugin } from "@univerjs/sheets-filter";
import { UniverSheetsFilterUIPlugin } from "./plugins/sheets-filter-ui/lib/index";

import { UniverFindReplacePlugin } from "@univerjs/find-replace";
import FindReplaceZhCN from "@univerjs/find-replace/locale/zh-CN";
import { UniverSheetsFindReplacePlugin } from "@univerjs/sheets-find-replace";
import SheetsFindReplaceZhCN from "@univerjs/sheets-find-replace/locale/zh-CN";

import "@univerjs/find-replace/lib/index.css";
import "@univerjs/sheets-find-replace/facade";

// import { UniverSheetsFilterUIPlugin } from "@univerjs/sheets-filter-ui";

import SheetsFilterUIZhCN from "@univerjs/sheets-filter-ui/locale/zh-CN";
import "./css/sheets-filter-ui/index.css";
import "@univerjs/sheets-filter/facade";

import "@univerjs/sheets-note-ui/lib/index.css";
import "@univerjs/sheets-note/facade";

import "@univerjs/engine-formula/facade";
import "@univerjs/ui/facade";
import "@univerjs/docs-ui/facade";
import "@univerjs/sheets/facade";
import "@univerjs/sheets-ui/facade";
import "@univerjs/sheets-formula/facade";
import "@univerjs/sheets-numfmt/facade";

import "@univerjs/design/lib/index.css";
import "@univerjs/ui/lib/index.css";
import "@univerjs/docs-ui/lib/index.css";
import "@univerjs/sheets-ui/lib/index.css";
import "@univerjs/sheets-formula-ui/lib/index.css";
import "@univerjs/sheets-numfmt-ui/lib/index.css";
import "./style.css";

// 创建 Univer 实例的函数
function createUniverInstance() {
  const univer = new Univer({
    locale: LocaleType.ZH_CN,
    locales: {
      [LocaleType.ZH_CN]: mergeLocales(
        DesignZhCN,
        UIZhCN,
        DocsUIZhCN,
        SheetsZhCN,
        SheetsUIZhCN,
        SheetsFormulaUIZhCN,
        SheetsNumfmtUIZhCN,
        SheetsDataValidationZhCN,
        SheetsFilterUIZhCN,
        FindReplaceZhCN,
        SheetsFindReplaceZhCN
      ),
    },
  });
  univer.registerPlugin(UniverRenderEnginePlugin);
  univer.registerPlugin(UniverFormulaEnginePlugin);

  univer.registerPlugin(UniverUIPlugin, {
    container: "app",
    // 禁用头部工具栏
    header: false,
    toolbar: false,
    // 禁用底部所有组件
    footer: false,
    // 保留右键菜单但限制功能
    contextMenu: true,
    showEditOnDropdown: true,
    // 自定义菜单配置，只保留基本的复制粘贴插入删除功能
    menu: {
      "sheet.contextMenu.permission": { hidden: true },
      "sheet.menu.sheet-frozen": { hidden: true },
      "sheet.menu.paste-special": { hidden: true },
      "sheet.menu.clear-selection": { hidden: true },
      "sheet.command.set-row-height": { hidden: true },
      "sheet.command.set-worksheet-col-width": { hidden: true },
      "sheet.command.insert-multi-cols-before": { hidden: true },
      "sheet.command.insert-multi-cols-right": { hidden: true },
      "sheet.command.hide-col-confirm": { hidden: true },
      "sheet.command.set-col-auto-width": { hidden: true },

      // 隐藏其他高级功能

      "sheet.menu.delete": { hidden: true },
      "sheet.menu.cell-insert": { hidden: true },
      "sheet.command.insert-multi-rows-above": { hidden: true },
      "sheet.command.insert-multi-rows-after": { hidden: true },
      "sheet.command.hide-row-confirm": { hidden: true },
      "sheet.command.set-row-is-auto-height": { hidden: true },

      // 保留基本编辑功能（这些通常默认显示，明确设置为显示）
      "sheet.command.copy": { hidden: false },
      "sheet.command.paste": { hidden: false },
      "sheet.command.cut": { hidden: true },
      "sheet.command.undo": { hidden: true },
      "sheet.command.redo": { hidden: true },

      "sheet.operation.add-note-popup": { hidden: true },
      "sheet.command.delete-note": { hidden: true },
      "sheet.command.toggle-note-popup": { hidden: true },
      "sheet.menu.sheets-sort-ctx-popup": { hidden: true },
      "sheet.menu.sheets-sort": { hidden: true },
    },
    // customComponents: new Set([UNIVER_SHEET_PERMISSION_BACKGROUND]),
  });
  univer.registerPlugin(UniverDocsPlugin);
  univer.registerPlugin(UniverDocsUIPlugin);
  univer.registerPlugin(UniverSheetsPlugin);
  univer.registerPlugin(UniverSheetsUIPlugin, {
    protectedRangeShadow: false, // 禁用保护区域阴影
  });
  univer.registerPlugin(UniverSheetsFormulaPlugin);
  univer.registerPlugin(UniverSheetsFormulaUIPlugin);
  univer.registerPlugin(UniverSheetsNumfmtPlugin);
  univer.registerPlugin(UniverSheetsNumfmtUIPlugin);
  univer.registerPlugin(UniverDataValidationPlugin);
  univer.registerPlugin(UniverSheetsDataValidationPlugin);
  univer.registerPlugin(UniverSheetsDataValidationUIPlugin);
  univer.registerPlugin(UniverSheetsFilterPlugin);
  univer.registerPlugin(UniverSheetsFilterUIPlugin);
  univer.registerPlugin(UniverFindReplacePlugin);
  univer.registerPlugin(UniverSheetsFindReplacePlugin);
  univer.createUnit(UniverInstanceType.UNIVER_SHEET);

  const univerAPI = FUniver.newAPI(univer);

  return { univerAPI, univer };
}

// 将函数挂载到 window 对象，保持向后兼容
window.createUniver = createUniverInstance;

// 导出给 IIFE 格式使用
export { createUniverInstance as createUniver };
